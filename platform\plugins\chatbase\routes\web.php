<?php

use Bo<PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Admin\ChatbotController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Settings\ChatbaseSettingController;
use Illuminate\Support\Facades\Route;

// Admin routes
AdminHelper::registerRoutes(function (): void {
    Route::group([
        'prefix' => 'chatbase',
        'as' => 'chatbase.',
        'permission' => 'chatbase.settings',
    ], function (): void {
        Route::get('settings', [ChatbaseSettingController::class, 'edit'])->name('settings');
        Route::put('settings', [ChatbaseSettingController::class, 'update'])->name('settings.update');

        // Admin chatbot management - following ads plugin pattern
        Route::group(['prefix' => 'chatbots', 'as' => 'chatbots.'], function (): void {
            Route::resource('', ChatbotController::class)->parameters(['' => 'chatbot']);
        });
    });
});
