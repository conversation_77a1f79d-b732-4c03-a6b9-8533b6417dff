<?php

namespace Bo<PERSON><PERSON>\Chatbase\Tables;

use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Botble\Table\HeaderActions\CreateHeaderAction;
use Illuminate\Database\Eloquent\Builder;

class ChatbotTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->addColumns([
                IdColumn::make(),
                NameColumn::make()->route('chatbase.chatbots.edit'),
                Column::make('store_name')
                    ->title(trans('plugins/chatbase::chatbase.admin.chatbot.store'))
                    ->alignStart()
                    ->getValueUsing(function (Column $column) {
                        $item = $column->getItem();
                        if ($item->store) {
                            try {
                                return Html::link(
                                    route('marketplace.store.edit', $item->store->id),
                                    $item->store->name,
                                    ['target' => '_blank']
                                );
                            } catch (\Exception $e) {
                                return $item->store->name ?? "Store ID: {$item->store_id}";
                            }
                        }
                        return $item->store_id ? "Store ID: {$item->store_id}" : '—';
                    }),
                Column::make('customer_name')
                    ->title(trans('plugins/chatbase::chatbase.admin.chatbot.customer'))
                    ->alignStart()
                    ->getValueUsing(function (Column $column) {
                        $item = $column->getItem();
                        if ($item->customer) {
                            try {
                                return Html::link(
                                    route('customers.edit', $item->customer->id),
                                    $item->customer->name,
                                    ['target' => '_blank']
                                );
                            } catch (\Exception $e) {
                                return $item->customer->name ?? "Customer ID: {$item->customer_id}";
                            }
                        }
                        return $item->customer_id ? "Customer ID: {$item->customer_id}" : '—';
                    }),
                Column::make('chatbot_id')
                    ->title(trans('plugins/chatbase::chatbase.admin.chatbot.chatbot_id'))
                    ->alignStart()
                    ->getValueUsing(function (Column $column) {
                        $item = $column->getItem();
                        return $item->chatbot_id ? Html::tag('code', $item->chatbot_id) : '—';
                    }),
                StatusColumn::make(),
                CreatedAtColumn::make(),
            ])
            ->addHeaderAction(CreateHeaderAction::make()->route('chatbase.chatbots.create'))
            ->addActions([
                EditAction::make()->route('chatbase.chatbots.edit'),
                DeleteAction::make()->route('chatbase.chatbots.destroy'),
            ])
            ->addBulkAction(DeleteBulkAction::make()->permission('chatbase.settings'))
            ->addBulkChanges([
                NameBulkChange::make(),
                StatusBulkChange::make(),
            ])
            ->queryUsing(function (Builder $query) {
                return $query
                    ->select([
                        'id',
                        'name',
                        'description',
                        'store_id',
                        'customer_id',
                        'chatbot_id',
                        'status',
                        'created_at',
                        'last_trained_at',
                        'last_synced_at',
                    ])
                    ->with(['store', 'customer']);
            });
    }
}
